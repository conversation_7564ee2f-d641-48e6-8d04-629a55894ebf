"use client";

import { useSyncUserAccounts } from "@/api-hooks/useSyncUserAccounts";
import { useEffect } from "react";

import CRMRequiredRoute from "@/web/components/crm-required-route";
import MainContentWrapper from "@/web/components/main-content-wrapper";
import Sidebar from "@/web/components/sidebar/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/web/components/ui/sidebar";

function SyncUserAccounts({ children }: { children: React.ReactNode }) {
  const { mutate } = useSyncUserAccounts();

  // Trigger synchronization only once when the component mounts
  useEffect(() => {
    mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <>{children}</>;
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <CRMRequiredRoute>
      <SyncUserAccounts>
        <SidebarProvider>
          <Sidebar />
          <SidebarInset>
            <MainContentWrapper>{children}</MainContentWrapper>
          </SidebarInset>
        </SidebarProvider>
      </SyncUserAccounts>
    </CRMRequiredRoute>
  );
}
