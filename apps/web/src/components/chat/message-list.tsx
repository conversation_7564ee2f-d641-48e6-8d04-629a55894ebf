import { UIMessage } from "ai";
import { Fragment } from "react";

import Card<PERSON><PERSON><PERSON>all from "@/web/components/chat/card-tool-call";
import Message from "@/web/components/chat/message";

interface MessageListProps {
  messages: UIMessage[];
  addToolResult: (obj: {
    toolCallId: string;
    result: { action: "continue" | "abort" };
  }) => void;
}

// TODO: have this come from the backend
const HUMAN_REVIEW_REQUIRED_TOOLS = [
  "update_opportunity",
  "update_account",
  "create_contact",
  "update_contact",
  "create_task",
  "update_task",
  "create_event",
  "update_event",
];

export default function MessageList({
  messages,
  addToolResult,
}: MessageListProps) {
  return (
    <div className="w-3xl space-y-4 pb-4">
      {messages.map((message) => (
        <Fragment key={message.id}>
          {message.parts.map((part, index) => {
            if (part.type === "text") {
              return (
                <Message key={index} content={part.text} role={message.role} />
              );
            } else if (
              part.type === "tool-invocation" &&
              HUMAN_REVIEW_REQUIRED_TOOLS.includes(part.toolInvocation.toolName)
            ) {
              return (
                <CardToolCall
                  key={index}
                  toolInvocation={part.toolInvocation}
                  addToolResult={addToolResult}
                />
              );
            }
            return null; // TODO: Handle other part types if needed
          })}
        </Fragment>
      ))}
    </div>
  );
}
