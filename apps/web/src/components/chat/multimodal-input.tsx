"use client";

import { Send, Square } from "lucide-react";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useRef,
} from "react";
import { AIInput, AIInputTextarea } from "@/web/components/ui/kibo/input";

import type { ChatRequestOptions, Message } from "ai";
import type React from "react";

import { cn } from "@/web/lib/utils";
import { useIsMobile } from "@/web/hooks/use-mobile";

export default function MultimodalInput({
  input,
  setInput,
  isLoading,
  stop,
  setMessages,
  handleSubmit,
  className,
}: {
  input: string;
  setInput: (value: string) => void;
  isLoading: boolean;
  stop: () => void;
  setMessages: Dispatch<SetStateAction<Array<Message>>>;
  handleSubmit: (
    event?: {
      preventDefault?: () => void;
    },
    chatRequestOptions?: ChatRequestOptions,
  ) => void;
  className?: string;
}) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (textareaRef.current) {
      adjustHeight();
    }
  }, []);

  const adjustHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
    }
  };

  useEffect(() => {
    if (textareaRef.current) {
      const domValue = textareaRef.current.value;
      // Prefer DOM value over localStorage to handle hydration
      const finalValue = domValue || "";
      setInput(finalValue);
      adjustHeight();
    }
    // Only run once after hydration
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(event.target.value);
    adjustHeight();
  };

  const submitForm = useCallback(() => {
    handleSubmit(undefined, {});
  }, [handleSubmit]);

  return (
    <div
      className={cn(
        "flex w-full justify-center px-4",
        isMobile ? "bg-background fixed bottom-0 left-0 right-0" : "mb-5",
      )}
    >
      <div className="w-full max-w-[740px]">
        <AIInput onSubmit={submitForm}>
          <AIInputTextarea
            value={input}
            onChange={handleInput}
            placeholder="Send a message..."
            disabled={isLoading}
          />
        </AIInput>
      </div>
    </div>
  );
}

{
  /* <div className="relative flex w-full flex-col gap-4">
      <Textarea
        ref={textareaRef}
        placeholder="Send a message..."
        value={input}
        onChange={handleInput}
        className={cn(
          "bg-muted max-h-[calc(75dvh)] min-h-[24px] resize-none overflow-hidden rounded-xl !text-base",
          className,
        )}
        rows={3}
        autoFocus
        onKeyDown={(event) => {
          if (event.key === "Enter" && !event.shiftKey) {
            event.preventDefault();

            if (isLoading) {
              console.log("Please wait for the model to finish its response!");
            } else {
              submitForm();
            }
          }
        }}
      />

      {isLoading ? (
        <Button
          className="absolute bottom-2 right-2 top-1 m-0.5 h-fit rounded-full border p-1.5 dark:border-zinc-600"
          onClick={(event) => {
            event.preventDefault();
            stop();
            setMessages((messages) => messages);
          }}
        >
          <Square className="h-4 w-4" />
        </Button>
      ) : (
        <Button
          className="absolute bottom-2 right-2 top-1 m-0.5 h-fit rounded-full border p-1.5 dark:border-zinc-600"
          onClick={(event) => {
            event.preventDefault();
            submitForm();
          }}
          disabled={input.length === 0}
        >
          <Send className="h-4 w-4" />
        </Button>
      )}
    </div> */
}
